package testsmell;

/**
 * There are the thresholds for the test smells detection proposed by <PERSON><PERSON><PERSON> et.al. in
 * the paper _Investigating severity thresholds for test smells_.
 */
public class DetectionThresholds {

    public static int EAGER_TEST = 4;
    public static int ASSERTION_ROULETTE = 3;
    public static int VERBOSE_TEST = 13;
    public static int CONDITIONAL_TEST_LOGIC = 0;
    public static int MAGIC_NUMBER_TEST = 0;
    public static int GENERAL_FIXTURE = 0;
    public static int MYSTERY_GUEST = 0;
    public static int RESOURCE_OPTIMISM = 0;
    public static int SLEEPY_TEST = 0;
}
